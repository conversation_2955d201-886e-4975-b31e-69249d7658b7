# 🏗️ دليل شامل لنظام تأجير المعدات (Equipment Rental System)

## 📋 نظرة عامة على المشروع

هذا موقع ويب متكامل لتأجير المعدات والآلات الثقيلة، تم بناؤه باستخدام تقنيات حديثة ومتطورة. الموقع يوفر منصة شاملة لإدارة وتأجير المعدات مع لوحة تحكم إدارية متقدمة.

## 🛠️ التقنيات المستخدمة

### 1. **Backend (الخادم الخلفي)**

- **Laravel 9.19** - إطار عمل PHP الأكثر شعبية
- **PHP 8.0.2+** - لغة البرمجة الأساسية
- **MySQL** - قاعدة البيانات
- **Laravel Sanctum** - نظام المصادقة والـ API
- **Laravel Breeze** - نظام المصادقة الأساسي

### 2. **Frontend (الواجهة الأمامية)**

- **Blade Templates** - محرك القوالب في Laravel
- **Tailwind CSS 3.1** - إطار عمل CSS للتصميم
- **Alpine.js 3.4** - مكتبة JavaScript خفيفة للتفاعل
- **Vite 4.0** - أداة البناء والتطوير السريعة

### 3. **مكتبات إضافية**

- **Charts.js** - لعرض الرسوم البيانية والإحصائيات
- **Maatwebsite Excel** - لتصدير واستيراد ملفات Excel
- **Guzzle HTTP** - للتعامل مع طلبات HTTP

## 🏗️ بنية المشروع (Architecture)

### 1. **نمط MVC (Model-View-Controller)**

```
app/
├── Models/          # النماذج (قاعدة البيانات)
├── Controllers/     # المتحكمات (منطق العمل)
├── Views/          # العروض (واجهة المستخدم)
└── Middleware/     # الوسطاء (الحماية والتحقق)
```

### 2. **النماذج الأساسية (Models)**

#### **User Model** 👤

- إدارة المستخدمين والمديرين
- نظام الأدوار (user/admin)
- المصادقة والتسجيل

#### **Product Model** 🚜

- المعدات والآلات المتاحة للتأجير
- معلومات تفصيلية: الوزن، نوع الوقود، العلامة التجارية
- الكمية المتاحة والمدينة
- نظام المنتجات المميزة

#### **Category Model** 📂

- تصنيف المعدات (حفارات، رافعات، إلخ)
- صور للفئات
- ربط المنتجات بالفئات

#### **Rental Model** 📅

- إدارة عمليات التأجير
- تواريخ البداية والنهاية
- الموقع والحالة
- ربط العميل بالمنتج والعامل

#### **Worker Model** 👷

- العمال المختصين بتشغيل المعدات
- معلومات الاتصال والحالة
- الأسعار اليومية
- ربط العمال بالمعدات

#### **Contact Model** 📧

- رسائل التواصل من العملاء
- نظام إدارة الاستفسارات

### 3. **المتحكمات (Controllers)**

#### **متحكمات المدير (Admin Controllers)**

- `DashboardController` - لوحة التحكم والإحصائيات
- `CategoryController` - إدارة الفئات
- `ProductController` - إدارة المنتجات
- `RentalAdminController` - إدارة التأجيرات
- `WorkerController` - إدارة العمال

#### **متحكمات العملاء (User Controllers)**

- `LandingController` - الصفحة الرئيسية
- `ShopController` - متجر المعدات
- `RentalController` - عمليات التأجير
- `ProfileController` - إدارة الملف الشخصي

## 🗄️ قاعدة البيانات (Database Structure)

### الجداول الأساسية:

1. **users** - المستخدمين والمديرين

   - id, name, email, password, role, email_verified_at

2. **categories** - فئات المعدات

   - id, name, image

3. **products** - المعدات والآلات

   - id, category_id, name, description, price_per_day, image, weight, fuel_type, brand, dimensions, quantity_available, city, featured

4. **rentals** - عمليات التأجير

   - id, user_id, product_id, worker_id, start_date, end_date, location, status

5. **workers** - العمال المختصين

   - id, name, phone, email, image, status, city, price_per_day

6. **worker_equipment** - ربط العمال بالمعدات

   - id, worker_id, product_id

7. **contacts** - رسائل التواصل
   - id, name, email, subject, message

### العلاقات بين الجداول:

- **One-to-Many**: فئة واحدة ← عدة منتجات
- **One-to-Many**: مستخدم واحد ← عدة تأجيرات
- **Many-to-Many**: عمال ← معدات (عامل يمكنه تشغيل عدة معدات)

## 🔐 نظام الأمان والمصادقة

### 1. **نظام الأدوار**

- **Admin**: وصول كامل للوحة التحكم
- **User**: تصفح وتأجير المعدات فقط

### 2. **الحماية (Middleware)**

- `auth` - التحقق من تسجيل الدخول
- `is_admin` - التحقق من صلاحيات المدير
- `verified` - التحقق من البريد الإلكتروني

### 3. **Laravel Sanctum**

- حماية API endpoints
- إدارة الرموز المميزة (tokens)

## 🎨 واجهة المستخدم (UI/UX)

### 1. **التصميم**

- **Tailwind CSS** للتصميم المتجاوب
- **Alpine.js** للتفاعل الديناميكي
- تصميم حديث ومتجاوب مع جميع الأجهزة

### 2. **الصفحات الرئيسية**

- **الصفحة الرئيسية**: عرض المنتجات المميزة والأكثر تأجيراً
- **المتجر**: تصفح جميع المعدات مع الفلترة
- **صفحة المنتج**: تفاصيل المعدة وإمكانية التأجير
- **لوحة التحكم**: إحصائيات وإدارة شاملة

## 🔄 كيفية عمل النظام

### 1. **للعملاء**

1. التسجيل/تسجيل الدخول
2. تصفح المعدات حسب الفئات
3. اختيار المعدة والتواريخ
4. اختيار عامل مختص (اختياري)
5. تأكيد الحجز
6. متابعة حالة التأجير

### 2. **للمديرين**

1. إدارة الفئات والمنتجات
2. إدارة العمال والمختصين
3. مراجعة وإدارة التأجيرات
4. عرض الإحصائيات والتقارير
5. الرد على استفسارات العملاء

## 📊 نظام الإحصائيات والتقارير

- **Charts.js** لعرض الرسوم البيانية
- إحصائيات التأجيرات الشهرية
- أكثر المعدات تأجيراً
- إيرادات المبيعات
- تقارير Excel قابلة للتصدير

## 🚀 كيفية البدء والتطوير

### 1. **متطلبات النظام**

```bash
- PHP 8.0+
- Composer
- Node.js & NPM
- MySQL
- Git
```

### 2. **خطوات التثبيت**

```bash
# 1. استنساخ المشروع
git clone [repository-url]

# 2. تثبيت dependencies الـ PHP
composer install

# 3. تثبيت dependencies الـ JavaScript
npm install

# 4. إعداد ملف البيئة
cp .env.example .env

# 5. توليد مفتاح التطبيق
php artisan key:generate

# 6. إعداد قاعدة البيانات
php artisan migrate

# 7. تشغيل الخادم
php artisan serve

# 8. تشغيل Vite للتطوير
npm run dev
```

### 3. **بنية الملفات المهمة**

```
Equipment_Rental/
├── app/
│   ├── Models/              # نماذج قاعدة البيانات
│   ├── Http/Controllers/    # المتحكمات
│   └── Http/Middleware/     # الوسطاء
├── database/
│   ├── migrations/          # هجرات قاعدة البيانات
│   └── seeders/            # بيانات أولية
├── resources/
│   ├── views/              # قوالب Blade
│   ├── css/                # ملفات CSS
│   └── js/                 # ملفات JavaScript
├── routes/
│   ├── web.php             # مسارات الويب
│   └── api.php             # مسارات API
└── public/                 # الملفات العامة
```

## 🔧 API والتكامل

### 1. **Laravel Sanctum API**

- نقطة نهاية للمصادقة: `/api/user`
- حماية بالرموز المميزة
- إمكانية التوسع لتطبيقات الجوال

### 2. **إمكانيات التوسع**

- إضافة API endpoints جديدة
- تكامل مع تطبيقات الجوال
- ربط مع أنظمة دفع خارجية
- تكامل مع خرائط Google

## 📱 الميزات المتقدمة

### 1. **نظام البحث والفلترة**

- البحث بالاسم والفئة
- فلترة حسب المدينة والسعر
- ترتيب النتائج

### 2. **إدارة الملفات**

- رفع صور المنتجات
- رفع صور العمال
- تحسين الصور تلقائياً

### 3. **نظام الإشعارات**

- إشعارات حالة التأجير
- تذكيرات انتهاء التأجير
- إشعارات الرسائل الجديدة

## 🎯 نصائح للتطوير والتحسين

### 1. **الأداء**

- استخدام Laravel Cache
- تحسين استعلامات قاعدة البيانات
- ضغط الصور والملفات

### 2. **الأمان**

- تحديث Laravel بانتظام
- استخدام HTTPS
- تشفير البيانات الحساسة

### 3. **تجربة المستخدم**

- تحسين سرعة التحميل
- تصميم متجاوب
- رسائل خطأ واضحة

---

## 📊 المخططات التوضيحية التفصيلية

### 1. 🏗️ المخطط الشامل العام

```mermaid
graph TB
    %% User Interface Layer
    subgraph "طبقة واجهة المستخدم"
        LP[الصفحة الرئيسية<br/>Landing Page]
        SHOP[متجر المعدات<br/>Shop]
        PD[تفاصيل المنتج<br/>Product Details]
        RENT[صفحة التأجير<br/>Rental Form]
        DASH[لوحة التحكم<br/>Dashboard]
        AUTH[صفحات المصادقة<br/>Auth Pages]
    end

    %% Controllers Layer
    subgraph "طبقة المتحكمات"
        LC[LandingController]
        SC[ShopController]
        RC[RentalController]
        AC[AdminController]
        PC[ProductController]
        CC[CategoryController]
        WC[WorkerController]
    end

    %% Models Layer
    subgraph "طبقة النماذج"
        USER[User Model<br/>👤 المستخدمين]
        PROD[Product Model<br/>🚜 المعدات]
        CAT[Category Model<br/>📂 الفئات]
        RENTAL[Rental Model<br/>📅 التأجيرات]
        WORKER[Worker Model<br/>👷 العمال]
        CONTACT[Contact Model<br/>📧 الرسائل]
    end

    %% Database Layer
    subgraph "قاعدة البيانات"
        UT[users table]
        PT[products table]
        CT[categories table]
        RT[rentals table]
        WT[workers table]
        WET[worker_equipment table]
        COT[contacts table]
    end

    %% Technology Stack
    subgraph "التقنيات المستخدمة"
        LARAVEL[Laravel 9.19<br/>🔧 Backend Framework]
        BLADE[Blade Templates<br/>📄 View Engine]
        TAILWIND[Tailwind CSS<br/>🎨 Styling]
        ALPINE[Alpine.js<br/>⚡ JavaScript]
        MYSQL[MySQL<br/>🗄️ Database]
        VITE[Vite<br/>⚡ Build Tool]
    end

    %% Connections
    LP --> LC
    SHOP --> SC
    LC --> USER
    LC --> PROD
    SC --> PROD
    USER --> UT
    PROD --> PT
```

### 2. 🗄️ مخطط قاعدة البيانات والعلاقات

```mermaid
erDiagram
    USERS {
        int id PK
        string name
        string email
        string password
        string role "admin/user"
        timestamp email_verified_at
        timestamp created_at
        timestamp updated_at
    }

    CATEGORIES {
        int id PK
        string name
        string image
        timestamp created_at
        timestamp updated_at
    }

    PRODUCTS {
        int id PK
        int category_id FK
        string name
        text description
        decimal price_per_day
        string image
        string weight
        string fuel_type
        string brand
        string dimensions
        int quantity_available
        string city
        boolean featured
        timestamp created_at
        timestamp updated_at
    }

    RENTALS {
        int id PK
        int user_id FK
        int product_id FK
        int worker_id FK
        date start_date
        date end_date
        string location
        string status "pending/confirmed/completed/cancelled"
        timestamp created_at
        timestamp updated_at
    }

    WORKERS {
        int id PK
        string name
        string phone
        string email
        string image
        string status "available/unavailable"
        string city
        decimal price_per_day
        timestamp created_at
        timestamp updated_at
    }

    WORKER_EQUIPMENT {
        int id PK
        int worker_id FK
        int product_id FK
        timestamp created_at
        timestamp updated_at
    }

    CONTACTS {
        int id PK
        string name
        string email
        string subject
        text message
        timestamp created_at
        timestamp updated_at
    }

    %% Relationships
    USERS ||--o{ RENTALS : "يقوم بـ"
    CATEGORIES ||--o{ PRODUCTS : "تحتوي على"
    PRODUCTS ||--o{ RENTALS : "يتم تأجيرها في"
    WORKERS ||--o{ RENTALS : "يعمل في"
    WORKERS ||--o{ WORKER_EQUIPMENT : "يشارك في"
    PRODUCTS ||--o{ WORKER_EQUIPMENT : "يتطلب"
```

### 3. 🔄 مخطط تدفق العمليات

```mermaid
flowchart TD
    %% User Journey
    A[زائر جديد] --> B{مسجل مسبقاً؟}
    B -->|لا| C[التسجيل]
    B -->|نعم| D[تسجيل الدخول]
    C --> D
    D --> E[الصفحة الرئيسية]

    E --> F[تصفح الفئات]
    E --> G[عرض المنتجات المميزة]
    E --> H[البحث عن معدة]

    F --> I[اختيار فئة]
    I --> J[عرض منتجات الفئة]
    G --> K[تفاصيل المنتج]
    H --> L[نتائج البحث]
    L --> K
    J --> K

    K --> M{متوفر للتأجير؟}
    M -->|لا| N[إشعار عدم التوفر]
    M -->|نعم| O[نموذج التأجير]

    O --> P[اختيار التواريخ]
    P --> Q[اختيار الموقع]
    Q --> R{يحتاج عامل؟}
    R -->|نعم| S[اختيار عامل]
    R -->|لا| T[مراجعة الطلب]
    S --> T

    T --> U[تأكيد الحجز]
    U --> V[حفظ في قاعدة البيانات]
    V --> W[إرسال إشعار للمدير]
    W --> X[صفحة تأكيد الحجز]

    %% Admin Journey
    Y[مدير النظام] --> Z[تسجيل دخول المدير]
    Z --> AA[لوحة التحكم]

    AA --> BB[إدارة الفئات]
    AA --> CC[إدارة المنتجات]
    AA --> DD[إدارة التأجيرات]
    AA --> EE[إدارة العمال]
    AA --> FF[عرض الإحصائيات]
    AA --> GG[إدارة الرسائل]

    BB --> HH[إضافة/تعديل/حذف فئة]
    CC --> II[إضافة/تعديل/حذف منتج]
    DD --> JJ[مراجعة طلبات التأجير]
    EE --> KK[إضافة/تعديل عامل]
    FF --> LL[عرض التقارير والرسوم البيانية]
    GG --> MM[الرد على الاستفسارات]

    JJ --> NN{قبول الطلب؟}
    NN -->|نعم| OO[تأكيد التأجير]
    NN -->|لا| PP[رفض التأجير]
    OO --> QQ[إشعار العميل بالقبول]
    PP --> RR[إشعار العميل بالرفض]
```

### 4. 🛠️ البنية التقنية والطبقات

```mermaid
graph TB
    %% Frontend Layer
    subgraph "Frontend - طبقة الواجهة الأمامية"
        subgraph "Blade Templates"
            LANDING[landing.blade.php]
            SHOP_INDEX[shop/index.blade.php]
            SHOP_SHOW[shop/show.blade.php]
            RENTAL_CREATE[rentals/create.blade.php]
            ADMIN_DASH[admin/dashboard.blade.php]
        end

        subgraph "Assets"
            CSS[Tailwind CSS]
            JS[Alpine.js]
            VITE_BUILD[Vite Build System]
        end
    end

    %% Middleware Layer
    subgraph "Middleware - طبقة الوسطاء"
        AUTH_MW[auth middleware]
        ADMIN_MW[is_admin middleware]
        VERIFIED_MW[verified middleware]
        GUEST_MW[guest middleware]
    end

    %% Routes Layer
    subgraph "Routes - طبقة التوجيه"
        WEB_ROUTES[web.php]
        API_ROUTES[api.php]
        AUTH_ROUTES[auth.php]
    end

    %% Controllers Layer
    subgraph "Controllers - طبقة المتحكمات"
        subgraph "Public Controllers"
            LANDING_CTRL[LandingController]
            SHOP_CTRL[ShopController]
            RENTAL_CTRL[RentalController]
        end

        subgraph "Admin Controllers"
            ADMIN_DASH_CTRL[DashboardController]
            PRODUCT_CTRL[ProductController]
            CATEGORY_CTRL[CategoryController]
            WORKER_CTRL[WorkerController]
        end
    end

    %% Models Layer
    subgraph "Models - طبقة النماذج"
        USER_MODEL[User Model]
        PRODUCT_MODEL[Product Model]
        CATEGORY_MODEL[Category Model]
        RENTAL_MODEL[Rental Model]
        WORKER_MODEL[Worker Model]
    end

    %% Database Layer
    subgraph "Database - طبقة البيانات"
        MYSQL_DB[(MySQL Database)]
        MIGRATIONS[Migrations]
        STORAGE[File Storage]
    end

    %% External Services
    subgraph "External Services"
        SANCTUM[Laravel Sanctum]
        BREEZE[Laravel Breeze]
        CHARTS[Charts.js]
        EXCEL[Excel Export]
    end

    %% Connections
    LANDING --> WEB_ROUTES
    WEB_ROUTES --> AUTH_MW
    AUTH_MW --> LANDING_CTRL
    LANDING_CTRL --> USER_MODEL
    USER_MODEL --> MYSQL_DB
    CSS --> VITE_BUILD
    SANCTUM --> AUTH_MW
```

---

## 📋 تفاصيل تقنية إضافية

### 🔧 ملفات التكوين المهمة

#### composer.json

```json
{
  "require": {
    "php": "^8.0.2",
    "laravel/framework": "^9.19",
    "laravel/sanctum": "^3.0",
    "consoletvs/charts": "^6.8",
    "maatwebsite/excel": "^1.1"
  }
}
```

#### package.json

```json
{
  "devDependencies": {
    "@tailwindcss/forms": "^0.5.2",
    "alpinejs": "^3.4.2",
    "tailwindcss": "^3.1.0",
    "vite": "^4.0.0"
  }
}
```

#### tailwind.config.js

```javascript
module.exports = {
  content: ["./resources/views/**/*.blade.php"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Figtree"],
      },
    },
  },
  plugins: [require("@tailwindcss/forms")],
};
```

### 🗂️ بنية المجلدات التفصيلية

```
Equipment_Rental-master/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Admin/
│   │   │   │   ├── CategoryController.php
│   │   │   │   ├── ProductController.php
│   │   │   │   ├── DashboardController.php
│   │   │   │   └── RentalAdminController.php
│   │   │   ├── Auth/
│   │   │   │   ├── RegisteredUserController.php
│   │   │   │   └── AuthenticatedSessionController.php
│   │   │   ├── LandingController.php
│   │   │   ├── ShopController.php
│   │   │   ├── RentalController.php
│   │   │   └── WorkerController.php
│   │   ├── Middleware/
│   │   │   └── IsAdmin.php
│   │   └── Requests/
│   ├── Models/
│   │   ├── User.php
│   │   ├── Product.php
│   │   ├── Category.php
│   │   ├── Rental.php
│   │   ├── Worker.php
│   │   └── Contact.php
│   └── Providers/
├── database/
│   ├── migrations/
│   │   ├── 2014_10_12_000000_create_users_table.php
│   │   ├── 2025_04_10_113230_create_categories_table.php
│   │   ├── 2025_04_10_113240_create_products_table.php
│   │   ├── 2025_04_10_113249_create_rentals_table.php
│   │   ├── 2025_04_16_170625_create_workers_table.php
│   │   └── 2025_04_16_170708_create_worker_equipment_table.php
│   └── seeders/
├── resources/
│   ├── views/
│   │   ├── admin/
│   │   │   ├── dashboard.blade.php
│   │   │   ├── categories/
│   │   │   ├── products/
│   │   │   ├── rentals/
│   │   │   └── workers/
│   │   ├── auth/
│   │   │   ├── login.blade.php
│   │   │   └── register.blade.php
│   │   ├── shop/
│   │   │   ├── index.blade.php
│   │   │   └── show.blade.php
│   │   ├── rentals/
│   │   │   ├── create.blade.php
│   │   │   └── index.blade.php
│   │   ├── layouts/
│   │   │   ├── app.blade.php
│   │   │   └── guest.blade.php
│   │   └── landing.blade.php
│   ├── css/
│   │   └── app.css
│   └── js/
│       └── app.js
├── routes/
│   ├── web.php
│   ├── api.php
│   └── auth.php
├── public/
│   ├── storage/
│   └── build/
├── storage/
│   └── app/
│       └── public/
├── composer.json
├── package.json
├── tailwind.config.js
├── vite.config.js
└── .env.example
```

---

## 💻 أمثلة الكود الأساسية

### 🔧 نموذج المنتج (Product Model)

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'category_id', 'name', 'description', 'price_per_day',
        'image', 'weight', 'fuel_type', 'brand', 'dimensions',
        'quantity_available', 'city', 'featured'
    ];

    // العلاقة مع الفئة
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // العلاقة مع التأجيرات
    public function rentals()
    {
        return $this->hasMany(Rental::class);
    }

    // العلاقة مع العمال
    public function workers()
    {
        return $this->belongsToMany(Worker::class, 'worker_equipment');
    }
}
```

### � متحكم المتجر (ShopController)

```php
<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;

class ShopController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with('category');

        // فلترة حسب الفئة
        if ($request->category) {
            $query->where('category_id', $request->category);
        }

        // فلترة حسب المدينة
        if ($request->city) {
            $query->where('city', $request->city);
        }

        // البحث بالاسم
        if ($request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $products = $query->paginate(12);
        $categories = Category::all();

        return view('shop.index', compact('products', 'categories'));
    }

    public function show(Product $product)
    {
        $product->load('category', 'workers');
        $relatedProducts = Product::where('category_id', $product->category_id)
                                 ->where('id', '!=', $product->id)
                                 ->take(4)
                                 ->get();

        return view('shop.show', compact('product', 'relatedProducts'));
    }
}
```

### 🛡️ وسيط المدير (IsAdmin Middleware)

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class IsAdmin
{
    public function handle($request, Closure $next)
    {
        if (Auth::check() && Auth::user()->role === 'admin') {
            return $next($request);
        }

        return redirect('/')->with('error', 'غير مصرح لك بالوصول لهذه الصفحة');
    }
}
```

---

## �🎯 الخلاصة النهائية

نظام تأجير المعدات هو تطبيق ويب متكامل ومتطور يستخدم أحدث التقنيات في تطوير الويب. النظام مبني بطريقة احترافية ويمكن توسيعه وتطويره بسهولة لتلبية احتياجات مختلفة.

### **🏆 المميزات الرئيسية:**

- ✅ **نظام إدارة شامل** - لوحة تحكم متقدمة للمديرين
- ✅ **واجهة مستخدم حديثة** - تصميم متجاوب باستخدام Tailwind CSS
- ✅ **أمان متقدم** - نظام أدوار ومصادقة قوي
- ✅ **قابلية التوسع** - بنية مرنة قابلة للتطوير
- ✅ **تقارير وإحصائيات** - رسوم بيانية وتحليلات متقدمة
- ✅ **API جاهز** - للتطبيقات المحمولة والتكامل الخارجي

### **🛠️ التقنيات المستخدمة:**

- **Backend**: Laravel 9.19 + PHP 8.0+
- **Frontend**: Blade Templates + Tailwind CSS + Alpine.js
- **Database**: MySQL مع Eloquent ORM
- **Build Tools**: Vite 4.0
- **Authentication**: Laravel Sanctum + Breeze
- **Charts**: Charts.js للإحصائيات
- **File Management**: Laravel Storage

### **📊 إحصائيات المشروع:**

- **7 نماذج** أساسية مترابطة
- **15+ متحكم** للوظائف المختلفة
- **20+ عرض** Blade للواجهات
- **15+ هجرة** لقاعدة البيانات
- **نظام أدوار** متقدم
- **API endpoints** جاهزة

هذا النظام يمثل **مثالاً ممتازاً** على كيفية بناء تطبيقات ويب حديثة ومتطورة باستخدام Laravel وأفضل الممارسات في التطوير.

---

_تم إنشاء هذا الدليل بواسطة Augment Agent - المساعد الذكي للبرمجة_ 🤖
